# 构建阶段
FROM --platform=$BUILDPLATFORM jianweisoft.cn/library/node:18 as build-stage
WORKDIR /app

# 复制 package.json 和 pnpm-lock.yaml
COPY package*.json ./
COPY pnpm-lock.yaml ./
RUN npm config set registry https://registry.npmmirror.com

RUN echo public-hoist-pattern[]=*@nextui-org/*>>~/.npmrc
# 安装 pnpm 并安装所有依赖
RUN npm install -g pnpm
RUN pnpm config set registry https://registry.npmmirror.com
RUN pnpm install --prod

# 做多语言的翻译
RUN pnpm run translate

# 复制所有文件并构建
COPY . .
COPY ./vite.config.ts /app/vite.config.ts
RUN pnpm run build

# 生产阶段
FROM --platform=$BUILDPLATFORM jianweisoft.cn/library/openresty/openresty:alpine as production-stage
RUN echo "https://mirrors.aliyun.com/alpine/v3.10/main/" > /etc/apk/repositories \
  && echo "https://mirrors.aliyun.com/alpine/v3.10/community/" >> /etc/apk/repositories
RUN apk update

# 安装 gettext 包
RUN apk add gettext

# 复制构建的文件到生产镜像
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 设置工作目录
WORKDIR /usr/local/openresty/nginx/conf

# 复制 Nginx 配置文件
COPY nginx.conf /usr/local/openresty/nginx/conf/nginx.conf.template

# 设置 entrypoint
ENTRYPOINT envsubst '$SCP_GO_UI_HOST $SCP_GO_UI_PORT $UI_START_PORT' < nginx.conf.template > nginx.conf && cat nginx.conf && nginx -g 'daemon off;'

EXPOSE 80

LABEL jw_version="1.1"