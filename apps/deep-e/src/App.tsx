import React, { useEffect, useState } from 'react';
import { useRoutes } from 'react-router-dom';
import { App as AntdApp } from 'antd';
import 'scmify-components/styles.css';
import { ErrorBoundary } from '@ant-design/pro-components';
import { Suspense } from 'react';
import { type RouteObject, Navigate, matchPath } from 'react-router-dom';
import { Layout, Loading } from '@/components';
import Page from '@/pages';
import { AllowedRoute } from './router/type';
import { createNestedRoutes } from './router';
import { useAuth } from '@/hooks';
import { ToastProvider } from '@/hooks/use-toast';
import { Toaster } from './components/ui/toaster';
import { AlertDialogProvider } from '@/hooks/useAlertDialog';

const { Login, NotFound } = Page;

function App() {
  function findBestMatch(path: string, allowedRoutes: Array<AllowedRoute>) {
    let bestMatch = null;
    let highestScore = 0;

    for (const route of allowedRoutes) {
      const match = matchPath(route.path, path);
      if (match) {
        const score = match.pathname.split('/').filter(Boolean).length;
        if (score > highestScore) {
          highestScore = score;
          bestMatch = route;
        }
      }
    }
    return bestMatch;
  }

  const [dynamicRoutes, setDynamicRoutes] = useState<RouteObject[]>([]);
  const { loading, authMenus } = useAuth([]);

  const staticRoutes: RouteObject[] = [
    {
      path: '/',
      element: <Layout />,
      children: [
        {
          index: true,
          element: <Navigate to='/home' />, // 将这里改为一个有效的路由
        },
        // {
        //   path: 'taskTemplate',
        //   element: (
        //     <React.Suspense>
        //       <Page.TaskTemplate />
        //     </React.Suspense>
        //   ),
        // },
        // {
        //   path: 'editTemplate',
        //   element: (
        //     <React.Suspense>
        //       <Page.EditTemplate />
        //     </React.Suspense>
        //   ),
        // },
        ...dynamicRoutes, // 在这里展开动态路由
        {
          path: '*',
          element: (
            <Suspense>
              <NotFound
                findBestMatch={(path: any) => findBestMatch(path, [])}
              />
            </Suspense>
          ),
        },
      ],
    },
    {
      path: '/login',
      children: [
        {
          index: true,
          element: (
            <Suspense>
              <Login />
            </Suspense>
          ),
        },
      ],
    },
  ];

  useEffect(() => {
    if (!window.location.pathname.includes('/login')) {
      const _routes = createNestedRoutes(authMenus);
      setDynamicRoutes(_routes);
    }
  }, [authMenus, loading]);
  // useEffect(() => {
  //   const handleStorageChange = (event: StorageEvent) => {
  //     if (location.pathname.includes('/login')) return;
  //     if (event.key === 'auth-store') {
  //       try {
  //         const stored = localStorage.getItem('auth-store');
  //         if (stored) {
  //           const parsed = JSON.parse(stored);
  //           useAuthStore.getState().setToken(parsed.token);
  //         }
  //       } catch (e) {
  //         console.error('Failed to parse auth-store from localStorage', e);
  //       }
  //     }
  //   };

  //   window.addEventListener('storage', handleStorageChange);

  //   return () => {
  //     window.removeEventListener('storage', handleStorageChange);
  //   };
  // }, []);

  const router = useRoutes(staticRoutes);

  // 如果不在登录页面且权限菜单正在加载，显示加载状态
  if (loading && !window.location.pathname.includes('/login')) {
    return <Loading />;
  }

  // 如果不在登录页面且权限菜单加载完成但动态路由还没生成，也显示加载状态
  if (
    !loading &&
    !window.location.pathname.includes('/login') &&
    dynamicRoutes.length === 0 &&
    authMenus.length > 0
  ) {
    return <Loading />;
  }

  return (
    <ToastProvider>
      <AlertDialogProvider>
        <AntdApp
          message={{
            maxCount: 2,
            duration: 2,
          }}>
          <ErrorBoundary>
            {router}
            <Toaster />
          </ErrorBoundary>
        </AntdApp>
      </AlertDialogProvider>
    </ToastProvider>
  );
}

export default App;
