import { useState, useEffect } from 'react';
import { route_info, type RouteInfoParams } from '@/router';
import { AllowedRoute } from '@/router/type.ts';
import ajax from '@/api';
import _ from 'lodash';
import { useAuthStore } from '@/store/features/auth';

export default function useAuth(initMenus: AllowedRoute[]) {
  const [authMenus, setAuthMenus] = useState<Array<AllowedRoute>>(initMenus);
  const [loading, setLoading] = useState(true);
  const [menuList, setMenuList] = useState<RouteInfoParams[]>([]);
  const { token } = useAuthStore();

  const getAuth = async () => {
    try {
      const res = await ajax.getTierMenus();
      if (!res.code) {
        const list = _.get(res, 'data.list', []) || [];

        // 辅助函数，递归构建子菜单（多层路由匹配）

        // 辅助函数，递归构建子菜单（多层路由匹配）
        const buildTree = (
          parentPath: string,
          list: any[],
          routeInfoItem?: any,
        ): any[] => {
          const parentSegments = parentPath.split('/').filter(Boolean);

          // Get matching items from the API response
          let matchingItems = list
            .filter((item) => {
              if (!item.path.startsWith(parentPath + '/')) return false;
              const itemSegments = item.path.split('/').filter(Boolean);
              return itemSegments.length === parentSegments.length + 1;
            })
            .filter((item) => item.index !== false);

          // If we have route info children, sort according to their order
          if (routeInfoItem?.children?.length) {
            matchingItems = matchingItems.sort((a, b) => {
              const aIndex = routeInfoItem.children.findIndex((child: any) =>
                child.path.endsWith(a.path.split('/').pop()),
              );
              const bIndex = routeInfoItem.children.findIndex((child: any) =>
                child.path.endsWith(b.path.split('/').pop()),
              );
              return aIndex - bIndex;
            });
          }

          return matchingItems.map((item) => {
            const subtree = buildTree(item.path, list);
            const node: any = { name: item.menuName, path: item.path };
            if (subtree.length > 0) node.children = subtree;
            return node;
          });
        };

        // 在设置菜单列表时，传入route_info项
        setMenuList(() => {
          const _list = route_info.filter((item: any) => {
            return list.some((route: any) => route.path.startsWith(item.path));
          });
          _list.forEach((item: any) => {
            const match = list.find((route: any) => route.path === item.path);
            if (match && match.index !== false) {
              item.name = match.menuName;
            }
            const children = buildTree(item.path, list, item); // Pass the route_info item
            if (children.length > 0) {
              item.children = children;
            }
          });
          return _list.filter((item) => {
            const match = list.find((route: any) => route.path === item.path);
            return match?.index !== false;
          });
        });

        setAuthMenus(list); // 保持所有路由的权限，包括 index 为 false 的项
      }
    } catch (error) {
      console.error('错误:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!window.location.pathname.includes('/login') && token) {
      getAuth();
    } else if (window.location.pathname.includes('/login')) {
      // 在登录页面时重置状态
      setLoading(false);
      setAuthMenus([]);
      setMenuList([]);
    }
  }, [window.location.pathname, token]);

  return {
    authMenus,
    loading,
    menuList,
  };
}
