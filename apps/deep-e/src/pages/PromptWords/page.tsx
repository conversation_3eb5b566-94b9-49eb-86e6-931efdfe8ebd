import {
  Form,
  Select,
  Slider,
  InputNumber,
  Input,
  Button,
  Upload,
  Image,
  type GetProp,
  App,
} from 'antd';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Sender, Prompts } from '@ant-design/x';
import { CloudUploadOutlined, DeleteOutlined } from '@ant-design/icons';
import api from '@/api';
import { type AgentMessage } from './components/ChartView';
import { ModelList } from './type';

import { v4 as uuidv4 } from 'uuid';

import {
  getModelInfoListForSelect,
  getKnowledgeBaseListForSelect,
  uploadImagesForDeepPrompt,
  createSession,
  deleteSession,
  getPromptSingleOriginalImage,
} from './api';
import { useAuthStore } from '@/store/features';

import { ChatView, ChatViewProps } from './components';
import { promptsItems } from './constants';

export interface ModelConfig {
  modelName: string;
  modelConfigId: number;
  baseUuid: string;
  modelPath: string;
  taskUuid_base: string;
  taskUuid: string;
  modelType?: string; // 添加模型类型字段
  systemPrompt?: string;
  maxTokens: number;
  temperature: number;
  topP: number;
  topK: number;
}

interface FormFields {
  models: ModelConfig[];
}

interface ModelFieldsProps {
  field: any;
  form: any;
  clearHistory: () => void;
  resetFormToDefaults: () => void;
}

const ModelFields = ({
  field,
  form,
  clearHistory,
  resetFormToDefaults,
}: ModelFieldsProps) => {
  const index = field.name;
  const { message } = App.useApp();

  const updateField = (
    key: keyof ModelConfig,
    value: number | string | undefined | null,
  ) => {
    const models = form.getFieldValue('models') || [];
    models[index] = { ...models[index], [key]: value };
    form.setFieldsValue({ models });
  };

  const models = form.getFieldValue('models') || [];

  const [modelList, setModelList] = useState<ModelList[]>([]);
  const [knowledgeList, setKnowledgeList] = useState<any[]>([]);

  const [modeTypeList, setModeTypeList] = useState<any[]>([]);

  const getModeTypeListFetch = async () => {
    try {
      const res = await api.getModelTypeListForChat();
      if (!res.code) {
        setModeTypeList(
          res.data.list.map((item: string) => ({ label: item, value: item })),
        );
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error('Error fetching model types:', error);
    }
  };

  const getModelInfoListForSelectFetch = async (type: string) => {
    try {
      const res = await getModelInfoListForSelect(type);
      if (!res.code) {
        setModelList(res.data.list as any);
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error('Error fetching model config list:', error);
    }
  };

  const getKnowledgeBaseListForSelectFetch = async () => {
    try {
      const res = await getKnowledgeBaseListForSelect();
      if (!res.code) {
        setKnowledgeList(res.data.list);
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      console.error('Error fetching model config list:', error);
    }
  };

  useEffect(() => {
    // getModelInfoListForSelectFetch();
    getKnowledgeBaseListForSelectFetch();
    getModeTypeListFetch();
  }, []);

  useEffect(() => {
    if (models[index]['modelType'])
      getModelInfoListForSelectFetch(models[index]['modelType']);
  }, [models[index]['modelType']]);

  return (
    <div
      key={field.key}
      className='mb-6 border p-4 rounded-lg border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200 bg-white'>
      <Form.Item
        label={
          <span className='text-gray-700 font-medium'>{t('模型类型')}</span>
        }
        name={[index, 'modelType']}>
        <Select
          options={modeTypeList}
          onChange={(value: string) => {
            getModelInfoListForSelectFetch(value);

            // 1. 清除对话历史
            clearHistory();

            // 2. 重置左侧表单到默认值
            resetFormToDefaults();

            // 3. 更新所有Form.List项的ModelType以保持一致性
            const models = form.getFieldValue('models') || [];
            const updatedModels = models.map((model: ModelConfig) => ({
              ...model,
              modelType: value,
              // 重置模型选择相关字段
              taskUuid_base: undefined,
              modelConfigId: undefined,
              modelName: undefined,
              modelPath: undefined,
              taskUuid: undefined,
              // 重置其他配置到默认值
              systemPrompt: t(
                '你是一个智能、可靠、礼貌且高效的 AI 助手，请你根据用户的要求提供清晰、有条理、准确的回答。',
              ),
              maxTokens: 2048,
              temperature: 0.7,
              topP: 0.7,
              topK: 5,
              baseUuid: undefined,
            }));

            form.setFieldsValue({ models: updatedModels });
          }}
        />
      </Form.Item>
      <Form.Item
        label={<span className='text-gray-700 font-medium'>Model</span>}
        name={[index, 'taskUuid_base']}
        rules={[
          {
            required: true,
            message: t('请选择模型'),
            validator: (_, value) => {
              if (!value) {
                return Promise.reject(t('请先选择模型才能进行对话'));
              }
              return Promise.resolve();
            },
          },
        ]}>
        <Select
          className='w-full'
          placeholder={t('选择模型')}
          options={modelList.map((item) => ({
            title: item.groupName,
            label: <span>{item.groupName}</span>,
            options: item.modelInfoList.map((li, index) => {
              return {
                label: li.modelName,
                value: item.groupName + '$' + index,
              };
            }),
          }))}
          onChange={(value) => {
            const [groupName, modelIndex] = value.split('$');
            const model = modelList.find(
              (item) => item.groupName === groupName,
            );
            if (model && model.modelInfoList[Number(modelIndex)]) {
              updateField(
                'modelConfigId',
                model.modelInfoList[Number(modelIndex)].modelConfigId,
              );
              updateField(
                'modelName',
                model.modelInfoList[Number(modelIndex)].modelName,
              );
              updateField(
                'modelPath',
                model.modelInfoList[Number(modelIndex)].modelPath,
              );
              updateField(
                'taskUuid',
                model.modelInfoList[Number(modelIndex)].taskUuid,
              );
              console.log(model.modelInfoList[Number(modelIndex)]);
            }
            clearHistory();
          }}
        />
      </Form.Item>
      <Form.Item
        label={<span className='text-gray-700 font-medium'>System Prompt</span>}
        name={[index, 'systemPrompt']}
        initialValue={t(
          '你是一个智能、可靠、礼貌且高效的 AI 助手，请你根据用户的要求提供清晰、有条理、准确的回答。',
        )}>
        <Input.TextArea rows={2} />
      </Form.Item>
      <div className='mb-6'>
        <div className='flex justify-between items-center mb-1'>
          <span className='text-gray-700 font-medium'>Max Tokens</span>
          <Form.Item name={[index, 'maxTokens']} noStyle initialValue={2048}>
            <InputNumber
              min={0}
              max={10000}
              onChange={(value) => updateField('maxTokens', value)}
            />
          </Form.Item>
        </div>
        <Form.Item name={[index, 'maxTokens']} noStyle>
          <Slider min={0} max={10000} />
        </Form.Item>
      </div>
      <div className='mb-6'>
        <div className='flex justify-between items-center mb-1'>
          <span className='text-gray-700 font-medium'>Temperature</span>
          <Form.Item name={[index, 'temperature']} noStyle initialValue={0.7}>
            <InputNumber
              min={0}
              max={2}
              step={0.1}
              onChange={(value) => updateField('temperature', value)}
            />
          </Form.Item>
        </div>
        <Form.Item name={[index, 'temperature']} noStyle>
          <Slider min={0} max={1} step={0.1} />
        </Form.Item>
      </div>
      <div className='mb-6'>
        <div className='flex justify-between items-center mb-1'>
          <span className='text-gray-700 font-medium'>Top P</span>
          <Form.Item name={[index, 'topP']} noStyle initialValue={0.7}>
            <InputNumber
              min={0}
              max={1}
              step={0.1}
              onChange={(value) => updateField('topP', value)}
            />
          </Form.Item>
        </div>
        <Form.Item name={[index, 'topP']} noStyle>
          <Slider min={0} max={1} step={0.1} />
        </Form.Item>
      </div>
      <div className='mb-6'>
        <div className='flex justify-between items-center mb-1'>
          <span className='text-gray-700 font-medium'>Top K</span>
          <Form.Item name={[index, 'topK']} noStyle initialValue={5}>
            <InputNumber
              min={0}
              max={100}
              onChange={(value) => updateField('topK', value)}
            />
          </Form.Item>
        </div>
        <Form.Item name={[index, 'topK']} noStyle>
          <Slider min={0} max={100} />
        </Form.Item>
      </div>
      <div>
        <Form.Item label={t('知识库')} name={[index, 'baseUuid']}>
          <Select
            options={knowledgeList.map((item) => ({
              label: item.baseName,
              value: item.baseUuid,
            }))}
            allowClear
          />
        </Form.Item>
      </div>
    </div>
  );
};

export default function PromptWords() {
  const { message } = App.useApp();
  const [form] = Form.useForm<FormFields>();
  const [inputMessage, setInputMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string>('');
  const [images, setImages] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);
  const [pasteUploading, setPasteUploading] = useState(false); // 粘贴上传状态
  const [showImageUpload, setShowImageUpload] = useState(false); // 控制图片上传组件显示/隐藏
  const [imageCache, setImageCache] = useState<Map<string, string>>(new Map()); // 图片缓存
  const [uploadedImages, setUploadedImages] = useState<
    Array<{
      url: string;
      localUrl: string; // 本地 blob URL
      name: string;
      uid: string;
      fileName: string;
      file: File; // 保存原始文件对象
    }>
  >([]);

  const models = Form.useWatch('models', form) || [];
  const requestRefs = useRef<Array<(content: AgentMessage) => void>>([]);
  const cancelRefs = useRef<Array<() => void>>([]);
  const { token } = useAuthStore();

  const responsePromisesRef = useRef<Array<Promise<void>>>([]);
  const clearRefs = useRef<Array<() => void>>([]);
  // const senderRef = useRef<HTMLDivElement>(null); // Sender组件的ref

  const hasValidModel = useCallback(() => {
    if (models.length === 1) {
      return models[0]?.taskUuid_base;
    }
    return models.length === 2 && models.every((model) => model?.taskUuid_base);
  }, [models]);

  // 检查是否有vision-language类型的模型
  const hasVisionLanguageModel = useCallback(() => {
    return models.some((model) => model?.modelType === 'vision-language');
  }, [models]);

  // 监听模型类型变化，如果没有vision-language类型的模型，清空已上传的图片
  const prevHasVisionLanguageModel = useRef(hasVisionLanguageModel());

  useEffect(() => {
    const currentHasVisionLanguageModel = hasVisionLanguageModel();

    // 只有当从有vision-language模型变为没有时才清空图片
    if (prevHasVisionLanguageModel.current && !currentHasVisionLanguageModel) {
      // 释放所有本地 blob URL
      uploadedImages.forEach((img) => {
        if (img.localUrl) {
          URL.revokeObjectURL(img.localUrl);
        }
      });
      setImages([]);
      setUploadedImages([]);
    }

    prevHasVisionLanguageModel.current = currentHasVisionLanguageModel;
  }, [hasVisionLanguageModel]); // 只依赖hasVisionLanguageModel函数

  // 创建新会话
  const createNewSession = useCallback(async () => {
    try {
      const res = await createSession();
      if (res.code === 0 && res.data?.result) {
        setSessionId(res.data.result);
        return res.data.result;
      } else {
        throw new Error(res.msg || t('创建会话失败'));
      }
    } catch (error: any) {
      console.error(t('创建会话失败，使用备用方案:'), error);
      message.warning(t('会话创建失败，使用本地生成的会话ID'));
      // 降级使用 UUID 作为备用方案
      const fallbackSessionId = uuidv4();
      setSessionId(fallbackSessionId);
      return fallbackSessionId;
    }
  }, []);

  // 获取有效的图片URL - 使用新的接口获取图片，支持缓存
  const getValidImageUrl = useCallback(
    async (image: any): Promise<string> => {
      const cacheKey = `${sessionId}-${image.fileName}`;

      // 检查缓存
      if (imageCache.has(cacheKey)) {
        return imageCache.get(cacheKey)!;
      }

      // 如果有本地URL且仍然有效，优先使用
      if (image.localUrl) {
        try {
          const url = new URL(image.localUrl);
          if (url.protocol === 'blob:') {
            // 缓存本地URL，但避免触发状态更新
            imageCache.set(cacheKey, image.localUrl);
            return image.localUrl;
          }
        } catch (e) {
          console.warn(t('本地URL无效:'), image.localUrl);
        }
      }

      // 使用新的接口获取图片
      if (image.fileName && sessionId) {
        try {
          const response = await getPromptSingleOriginalImage(
            sessionId,
            image.fileName,
          );
          if (response.code === 0 && response.data?.result) {
            // 处理base64数据
            let base64Data = response.data.result;

            // 如果base64数据没有前缀，添加前缀
            if (!base64Data.startsWith('data:')) {
              base64Data = `data:image/jpeg;base64,${base64Data}`;
            }

            // 缓存base64数据，但避免触发状态更新
            imageCache.set(cacheKey, base64Data);
            return base64Data;
          }
        } catch (error) {
          console.error(t('获取图片失败:'), error);
        }
      }

      // 降级使用旧的服务器URL
      if (image.url) {
        imageCache.set(cacheKey, image.url);
        return image.url;
      }

      // 如果都没有，返回空字符串
      console.warn(t('图片没有有效的URL:'), image);
      return '';
    },
    [sessionId, imageCache],
  );

  // 全局图片获取函数 - 包含缓存逻辑，供 ChatView 使用
  const getImageByName = useCallback(
    async (imageName: string): Promise<string> => {
      const cacheKey = `${sessionId}-${imageName}`;

      // 检查缓存
      if (imageCache.has(cacheKey)) {
        return imageCache.get(cacheKey)!;
      }

      // 首先尝试从 uploadedImages 中获取本地URL
      const uploadedImage = uploadedImages.find(
        (item) => item.fileName === imageName,
      );

      if (uploadedImage?.localUrl) {
        try {
          const url = new URL(uploadedImage.localUrl);
          if (url.protocol === 'blob:') {
            // 缓存本地URL，但避免触发状态更新
            imageCache.set(cacheKey, uploadedImage.localUrl);
            return uploadedImage.localUrl;
          }
        } catch (e) {
          console.warn(t('本地URL无效:'), uploadedImage.localUrl);
        }
      }

      // 使用新的接口获取图片
      if (imageName && sessionId) {
        try {
          const response = await getPromptSingleOriginalImage(
            sessionId,
            imageName,
          );
          if (response.code === 0 && response.data?.result) {
            // 处理base64数据
            let base64Data = response.data.result;

            // 如果base64数据没有前缀，添加前缀
            if (!base64Data.startsWith('data:')) {
              base64Data = `data:image/jpeg;base64,${base64Data}`;
            }

            // 缓存base64数据，但避免触发状态更新
            imageCache.set(cacheKey, base64Data);
            return base64Data;
          }
        } catch (error) {
          console.error(t('获取图片失败:'), error);
        }
      }

      // 降级使用旧的服务器URL
      if (uploadedImage?.url) {
        imageCache.set(cacheKey, uploadedImage.url);
        return uploadedImage.url;
      }

      // 如果都没有，返回空字符串
      console.warn(t('图片没有有效的URL:'), imageName);
      return '';
    },
    [sessionId, imageCache, uploadedImages],
  );

  // 图片上传处理函数
  const uploadImages = useCallback(
    async (files: File[]) => {
      if (!sessionId) {
        message.error('会话ID未初始化，请刷新页面重试');
        return;
      }

      // 先创建本地预览图片
      const localImages = files.map((file, index) => ({
        url: '', // 服务器URL稍后填充
        localUrl: URL.createObjectURL(file), // 创建本地 blob URL
        name: file.name,
        uid: `local-${Date.now()}-${index}`,
        fileName: '', // 服务器文件名稍后填充
        file: file, // 保存原始文件对象
      }));

      // 立即显示本地预览
      setUploadedImages((prev) => [...prev, ...localImages]);

      setUploading(true);
      try {
        const res = await uploadImagesForDeepPrompt(sessionId, files);
        if (res.code === 0) {
          // 根据实际响应结构处理数据：res.data.list 是文件名数组
          const uploadedFileNames = res.data.list || [];

          // 更新本地图片信息，添加服务器返回的数据
          setUploadedImages((prev) => {
            const updatedImages = [...prev];
            localImages.forEach((localImg, index) => {
              const imgIndex = updatedImages.findIndex(
                (img) => img.uid === localImg.uid,
              );
              if (imgIndex !== -1 && uploadedFileNames[index]) {
                updatedImages[imgIndex] = {
                  ...updatedImages[imgIndex],
                  url: `/api/file/image/${uploadedFileNames[index]}`,
                  fileName: uploadedFileNames[index],
                };
              }
            });
            return updatedImages;
          });

          setImages((prev) => [...prev, ...uploadedFileNames]);
          message.success(
            t('成功上传{length} 张图片', { length: uploadedFileNames.length }),
          );
        } else {
          // 上传失败，移除本地预览
          setUploadedImages((prev) =>
            prev.filter(
              (img) => !localImages.some((local) => local.uid === img.uid),
            ),
          );
          // 释放本地 blob URL
          localImages.forEach((img) => URL.revokeObjectURL(img.localUrl));
          throw new Error(res.msg || t('上传失败'));
        }
      } catch (error: any) {
        console.error('图片上传失败:', error);
        message.error(t('图片上传失败:{message}', { message: error.message }));
        // 上传失败，移除本地预览并释放内存
        setUploadedImages((prev) =>
          prev.filter(
            (img) => !localImages.some((local) => local.uid === img.uid),
          ),
        );
        localImages.forEach((img) => URL.revokeObjectURL(img.localUrl));
      } finally {
        setUploading(false);
      }
    },
    [sessionId],
  );

  // 直接使用 uploadedImages，不需要额外的 allImages 状态
  // useEffect(() => {
  //   setAllImages((prev) => {
  //     // 将新上传的图片与现有图片合并
  //     const newImages = [...prev];
  //     uploadedImages.forEach((img) => {
  //       // 检查是否已存在相同uid的图片
  //       if (!newImages.some((existingImg) => existingImg.uid === img.uid)) {
  //         newImages.push(img);
  //       }
  //     });
  //     return newImages;
  //   });
  // }, [images]);

  // 删除单张图片
  const removeImage = useCallback(
    (uid: string) => {
      // 找到要删除的图片
      const imageToRemove = uploadedImages.find((img) => img.uid === uid);

      // 释放本地 blob URL
      if (imageToRemove?.localUrl) {
        URL.revokeObjectURL(imageToRemove.localUrl);
      }

      setUploadedImages((prev) => prev.filter((img) => img.uid !== uid));
      setImages((prev) => {
        if (imageToRemove?.fileName) {
          return prev.filter(
            (fileName: string) => fileName !== imageToRemove.fileName,
          );
        }
        return prev;
      });
    },
    [uploadedImages],
  );

  const requestChat = useCallback(
    (
      modelState: ChatViewProps['modelState'],
      messages: any,
      requestSessionId?: string,
      requestImages?: string[],
    ) => {
      const abortController = new AbortController();
      // 优先使用传入的 sessionId 和 images，如果没有则使用组件状态中的
      const finalSessionId = requestSessionId || sessionId;

      // 使用 uploadedImages 来构建图片数据，包含完整的信息
      const finalImages =
        requestImages ||
        uploadedImages.map((img) => img.fileName).filter(Boolean);

      return {
        request: fetch('/api/knowledge/queryByKnowledgeBase', {
          method: 'POST',
          headers: {
            'X-Token': token,
          } as any,
          body: JSON.stringify({
            ...modelState,
            isStream: true,
            messages: messages,
            sessionId: finalSessionId,
            images:
              finalImages.length > 0
                ? finalImages.map((item) => ({
                    data: item, // 使用 fileName 作为 imageId
                  }))
                : undefined,
          }),
          signal: abortController.signal,
        }),
        abort: abortController,
      };
    },
    [token, sessionId, uploadedImages],
  );

  // 组件初始化时创建会话
  useEffect(() => {
    createNewSession();
  }, [createNewSession]);

  // 清空上传的图片列表
  const clearUploadedImages = useCallback(() => {
    // 释放所有本地 blob URL
    // uploadedImages.forEach((img) => {
    // if (img.localUrl) {
    //   URL.revokeObjectURL(img.localUrl);
    // }
    // });

    // 清空状态
    setImages([]);
    setUploadedImages([]);
  }, [uploadedImages]);

  // 处理消息提交
  const handleSubmit = useCallback(() => {
    if (!inputMessage.trim()) return;
    setInputMessage('');
    setLoading(true);

    let completedCount = 0;
    const totalRequests = requestRefs.current.filter(Boolean).length;

    // 获取当前有效的图片文件名
    const currentImages = uploadedImages
      .map((img) => img.fileName)
      .filter(Boolean);

    requestRefs.current.forEach((request) => {
      if (request) {
        request({
          content: inputMessage,
          role: 'user',
          sessionId,
          images:
            currentImages.length > 0
              ? currentImages.map((item) => ({
                  data: item, // 使用 fileName 作为 imageId
                }))
              : undefined,
          onComplete: () => {
            completedCount++;
            if (completedCount === totalRequests) {
              setLoading(false);
            }
          },
        } as AgentMessage);
      }
    });

    // 发送消息后立即清空上传的图片列表
    clearUploadedImages();
  }, [inputMessage, sessionId, uploadedImages, clearUploadedImages]);

  // 处理取消操作
  const handleCancel = useCallback(() => {
    cancelRefs.current.forEach((cancel) => {
      if (cancel) {
        cancel();
      }
    });
    setLoading(false);
    responsePromisesRef.current = [];
  }, []);

  // 组件卸载时清理所有 blob URL
  useEffect(() => {
    return () => {
      uploadedImages.forEach((img) => {
        if (img.localUrl) {
          URL.revokeObjectURL(img.localUrl);
        }
      });
    };
  }, []);

  // 处理剪贴板图片粘贴上传
  const handlePasteImageUpload = useCallback(
    async (files: File[]) => {
      if (!hasVisionLanguageModel()) {
        message.warning(
          t('当前模型不支持图片上传，请选择vision-language类型的模型'),
        );
        return;
      }

      if (!sessionId) {
        message.error(t('会话未创建，请稍后重试'));
        return;
      }

      // 创建本地预览图片对象
      const localImages = files.map((file) => ({
        url: '',
        localUrl: URL.createObjectURL(file),
        name: file.name || `pasted-image-${Date.now()}.png`,
        uid: uuidv4(),
        fileName: '',
        file,
      }));

      // 立即显示本地预览
      setUploadedImages((prev) => [...prev, ...localImages]);

      setPasteUploading(true);
      try {
        const res = await uploadImagesForDeepPrompt(sessionId, files);
        if (res.code === 0) {
          // 根据实际响应结构处理数据：res.data.list 是文件名数组
          const uploadedFileNames = res.data.list || [];

          // 更新本地图片信息，添加服务器返回的数据
          setUploadedImages((prev) => {
            const updatedImages = [...prev];
            localImages.forEach((localImg, index) => {
              const imgIndex = updatedImages.findIndex(
                (img) => img.uid === localImg.uid,
              );
              if (imgIndex !== -1 && uploadedFileNames[index]) {
                updatedImages[imgIndex] = {
                  ...updatedImages[imgIndex],
                  fileName: uploadedFileNames[index],
                  url: uploadedFileNames[index], // 使用服务器返回的文件名作为URL
                };
              }
            });
            return updatedImages;
          });

          message.success(
            t('成功粘贴上传 {length} 张图片', {
              length: uploadedFileNames.length,
            }),
          );
        } else {
          // 上传失败，移除本地预览
          setUploadedImages((prev) =>
            prev.filter(
              (img) => !localImages.some((local) => local.uid === img.uid),
            ),
          );
          message.error(t('图片上传失败:{msg}', { msg: res.msg }));
        }
      } catch (error: any) {
        console.error(t('粘贴图片上传失败:'), error);
        message.error(t('粘贴图片上传失败:') + error.message);
        // 上传失败，移除本地预览并释放内存
        setUploadedImages((prev) =>
          prev.filter(
            (img) => !localImages.some((local) => local.uid === img.uid),
          ),
        );
        localImages.forEach((img) => URL.revokeObjectURL(img.localUrl));
      } finally {
        setPasteUploading(false);
      }
    },
    [hasVisionLanguageModel, sessionId, message],
  );

  // 添加粘贴事件监听器
  useEffect(() => {
    const handlePaste = async (event: ClipboardEvent) => {
      // 只有当模型类型为vision-language时才处理粘贴事件
      if (!hasVisionLanguageModel()) {
        return;
      }

      const clipboardData = event.clipboardData;
      if (!clipboardData) return;

      const items = Array.from(clipboardData.items);
      const imageFiles: File[] = [];

      // 检查剪贴板中的图片
      for (const item of items) {
        if (item.type.startsWith('image/')) {
          const file = item.getAsFile();
          if (file) {
            // 验证图片格式
            const validTypes = [
              'image/jpeg',
              'image/jpg',
              'image/png',
              'image/gif',
            ];
            if (validTypes.includes(file.type)) {
              // 验证文件大小 (5MB)
              if (file.size > 5 * 1024 * 1024) {
                message.error(t('图片大小不能超过 5MB'));
                continue;
              }
              imageFiles.push(file);
            } else {
              message.error(
                t('仅支持上传 JPG、JPEG、PNG 或 GIF 格式的图片文件'),
              );
            }
          }
        }
      }

      // 如果有图片文件，则上传
      if (imageFiles.length > 0) {
        event.preventDefault(); // 阻止默认粘贴行为
        await handlePasteImageUpload(imageFiles);
      }
    };

    // 添加全局粘贴事件监听器
    document.addEventListener('paste', handlePaste);

    return () => {
      document.removeEventListener('paste', handlePaste);
    };
  }, [hasVisionLanguageModel, handlePasteImageUpload, message]);

  // 稳定的图片组件 - 使用原生img标签避免重新加载
  const StableImage = React.memo(
    ({ image }: { image: any }) => {
      const [imageSrc, setImageSrc] = useState<string>('');
      const [hasError, setHasError] = useState(false);
      const srcRef = useRef<string>('');

      // 只在图片真正改变时更新src
      useEffect(() => {
        if (image.localUrl && srcRef.current !== image.localUrl) {
          srcRef.current = image.localUrl;
          setImageSrc(image.localUrl);
          setHasError(false);
        }
      }, [image.uid, image.localUrl]); // 只依赖uid和localUrl

      if (!imageSrc || hasError) {
        return (
          <div className='w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center'>
            <span className='text-xs text-gray-500'>{t('图片')}</span>
          </div>
        );
      }

      return (
        <div className='relative'>
          <img
            src={imageSrc}
            alt={image.name}
            className='w-16 h-16 object-cover rounded-lg border border-gray-200'
            onError={() => setHasError(true)}
            onLoad={() => setHasError(false)}
            style={{
              width: '64px',
              height: '64px',
              display: 'block',
            }}
          />
        </div>
      );
    },
    (prevProps, nextProps) => {
      // 严格比较：只有uid和localUrl都相同才认为相同
      return (
        prevProps.image.uid === nextProps.image.uid &&
        prevProps.image.localUrl === nextProps.image.localUrl
      );
    },
  );

  // 图片列表组件 - 使用 memo 避免不必要的重新渲染
  const ImageList = React.memo(
    ({
      images,
      onRemoveImage,
    }: {
      images: Array<{
        url: string;
        localUrl: string;
        name: string;
        uid: string;
        fileName: string;
        file: File;
      }>;
      onRemoveImage: (uid: string) => void;
    }) => {
      if (images.length === 0) {
        return null;
      }

      return (
        <div className='mb-3 p-3 bg-white rounded-lg border border-gray-200'>
          <div className='flex flex-wrap gap-2'>
            {images.map((image) => (
              <div key={image.uid} className='relative group'>
                <StableImage image={image} />
                <button
                  onClick={() => onRemoveImage(image.uid)}
                  className='absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity z-10'>
                  <DeleteOutlined />
                </button>
              </div>
            ))}
          </div>
        </div>
      );
    },
    (prevProps, nextProps) => {
      // 只有当图片数组长度或内容真正改变时才重新渲染
      if (prevProps.images.length !== nextProps.images.length) {
        return false;
      }

      // 检查每个图片的uid是否相同
      for (let i = 0; i < prevProps.images.length; i++) {
        if (prevProps.images[i].uid !== nextProps.images[i].uid) {
          return false;
        }
      }

      return true;
    },
  );

  // 图片上传按钮组件 - 使用 memo 避免重新渲染
  const ImageUploadButton = React.memo(() => {
    if (!hasVisionLanguageModel()) {
      return null;
    }

    return (
      <Button
        type='text'
        icon={<CloudUploadOutlined />}
        onClick={() => setShowImageUpload(!showImageUpload)}
        className={`flex items-center justify-center w-8 h-8 rounded transition-all duration-200 ease-in-out hover:bg-gray-100 ${showImageUpload ? 'text-blue-500 bg-blue-50' : 'text-gray-500'}`}
        title={t('图片上传')}
        style={{
          transform: showImageUpload ? 'scale(1.05)' : 'scale(1)',
          transition: 'all 0.2s ease-in-out',
        }}
      />
    );
  });

  // 重置表单到默认值的函数
  const resetFormToDefaults = useCallback(() => {
    const currentModels = form.getFieldValue('models') || [];
    const resetModels = currentModels.map((model: ModelConfig) => ({
      modelType: model.modelType, // 保持当前的modelType
      systemPrompt: t(
        '你是一个智能、可靠、礼貌且高效的 AI 助手，请你根据用户的要求提供清晰、有条理、准确的回答。',
      ),
      maxTokens: 2048,
      temperature: 0.7,
      topP: 0.7,
      topK: 5,
      // 清空其他字段
      taskUuid_base: undefined,
      modelConfigId: undefined,
      modelName: undefined,
      modelPath: undefined,
      taskUuid: undefined,
      baseUuid: undefined,
    }));

    form.setFieldsValue({ models: resetModels });
  }, [form]);

  const clearHistory = async () => {
    try {
      // 如果有当前会话ID，先删除会话
      if (sessionId) {
        await deleteSession(sessionId);
      }
    } catch (error: any) {
      console.error(t('删除会话失败:'), error);
      // 删除会话失败不影响清除操作，继续执行
      message.warning(t('删除会话失败，但将继续清除本地数据'));
    }

    // 释放所有本地 blob URL
    uploadedImages.forEach((img) => {
      if (img.localUrl) {
        URL.revokeObjectURL(img.localUrl);
      }
    });

    // 清空图片缓存
    setImageCache(new Map());

    // 创建新的 sessionId 并清空图片
    await createNewSession();
    setImages([]);
    setUploadedImages([]);

    clearRefs.current.forEach((clear) => {
      if (clear) clear();
    });
  };

  const onPromptsItemClick: GetProp<typeof Prompts, 'onItemClick'> = (info) => {
    if (!hasValidModel()) return;
    let completedCount = 0;
    const totalRequests = requestRefs.current.filter(Boolean).length;
    setLoading(true);

    // 获取当前有效的图片文件名
    const currentImages = uploadedImages
      .map((img) => img.fileName)
      .filter(Boolean);

    requestRefs.current.forEach((request) => {
      if (request) {
        request({
          content: info.data.description as string,
          role: 'user',
          sessionId,
          images:
            currentImages.length > 0
              ? currentImages.map((item) => ({
                  data: item, // 使用 fileName 作为 imageId
                }))
              : undefined,
          onComplete: () => {
            completedCount++;
            if (completedCount === totalRequests) {
              setLoading(false);
            }
          },
        } as AgentMessage);
      }
    });

    // 发送消息后立即清空上传的图片列表
    clearUploadedImages();
  };

  return (
    <div className='w-full h-full bg-gray-50 flex'>
      <div
        className='w-1/4 h-full overflow-auto px-4 py-6 border-r border-gray-200 
        [&::-webkit-scrollbar]:w-2 
        [&::-webkit-scrollbar-thumb]:bg-gray-300 
        [&::-webkit-scrollbar-thumb]:rounded-full
        [&::-webkit-scrollbar-track]:bg-transparent'>
        <Form className='space-y-4' form={form} layout='vertical'>
          <Form.Item name='models' initialValue={[{}]}>
            <Form.List name='models'>
              {(fields, { add }) => (
                <>
                  {fields.map((field) => (
                    <ModelFields
                      key={field.key}
                      field={field}
                      form={form}
                      clearHistory={clearHistory}
                      resetFormToDefaults={resetFormToDefaults}
                    />
                  ))}

                  {fields.length < 2 ? (
                    <Form.Item>
                      <Button
                        type='dashed'
                        onClick={add}
                        block
                        className='hover:border-blue-500 hover:text-blue-500'>
                        {t('添加模型')}
                      </Button>
                    </Form.Item>
                  ) : (
                    <div className='flex justify-around gap-4 mb-4'>
                      <Button
                        type='dashed'
                        danger
                        className='flex-1 hover:opacity-80'
                        onClick={() => {
                          const currentModels = form.getFieldValue('models');
                          currentModels.splice(1, 1);
                          form.setFieldsValue({ models: currentModels });
                          requestRefs.current.splice(1, 1);
                        }}>
                        {t('取消对比')}
                      </Button>
                      <Button
                        type='dashed'
                        className='flex-1 hover:border-blue-500 hover:text-blue-500'
                        onClick={() => {
                          const primary = form.getFieldValue('models')[0];
                          const currentModels = form.getFieldValue('models');
                          currentModels[1] = { ...primary };
                          form.setFieldsValue({ models: currentModels });
                        }}>
                        {t('同步参数')}
                      </Button>
                    </div>
                  )}
                </>
              )}
            </Form.List>
          </Form.Item>
        </Form>
      </div>
      <div className='flex-1 h-full flex flex-col p-6'>
        <div className='flex-1 h-full flex flex-col rounded-lg bg-white shadow-sm'>
          <div className='no-scrollbar full overflow-y-auto gap-2 p-4 box-border text-sm flex-1 flex flex-row'>
            {models.map((item, index: number) => {
              const model = item?.modelPath;
              const key = model + index || index;
              const currentModel = form.getFieldValue('models')[index] || {};

              return (
                <div className='w-full h-full' key={key}>
                  <ChatView
                    key={key}
                    modelState={{
                      modelName: currentModel.modelName,
                      modelConfigId: currentModel.modelConfigId,
                      baseUuid: currentModel.baseUuid,
                      modelPath: currentModel.modelPath,
                      taskUuid: currentModel.taskUuid,
                      systemPrompt: currentModel.systemPrompt ?? '',
                      maxTokens: currentModel.maxTokens,
                      temperature: currentModel.temperature,
                      topP: currentModel.topP,
                      topK: currentModel.topK,
                    }}
                    uploadedImages={uploadedImages}
                    sessionId={sessionId}
                    getImageByName={getImageByName}
                    requestChat={requestChat}
                    onRequestRef={(ref) => {
                      requestRefs.current[index] = ref;
                    }}
                    onCancelRef={(ref) => {
                      cancelRefs.current[index] = ref;
                    }}
                    onClearRef={(ref) => {
                      clearRefs.current[index] = ref;
                    }}
                  />
                </div>
              );
            })}
          </div>
          <div className='w-full bg-gray-50 rounded-lg p-4 transition-all duration-300 flex flex-col border-t border-gray-200'>
            <div className='flex justify-between mb-3 gap-2'>
              <Prompts items={promptsItems} onItemClick={onPromptsItemClick} />
              <Button
                type='text'
                danger
                className='hover:bg-red-50'
                onClick={clearHistory}
                disabled={!hasValidModel()}>
                {t('清除对话')}
              </Button>
            </div>

            {/* 图片上传区域 - 只有当模型类型为vision-language时才显示 */}
            {hasVisionLanguageModel() && (
              <ImageList images={uploadedImages} onRemoveImage={removeImage} />
            )}

            {/* 图片上传组件 - 只有当模型类型为vision-language时才显示 */}
            {hasVisionLanguageModel() && (
              <div
                className={`mb-3 overflow-hidden transition-all duration-300 ease-in-out ${
                  showImageUpload
                    ? 'max-h-96 opacity-100 transform scale-y-100'
                    : 'max-h-0 opacity-0 transform scale-y-0'
                }`}
                style={{
                  transformOrigin: 'top',
                  marginBottom: showImageUpload ? '0.75rem' : '0',
                }}>
                <div
                  className={`transition-all duration-300 ease-in-out ${
                    showImageUpload
                      ? 'translate-y-0 opacity-100'
                      : '-translate-y-4 opacity-0'
                  }`}>
                  <Upload.Dragger
                    accept='.jpg,.jpeg,.png'
                    multiple
                    showUploadList={false}
                    beforeUpload={(file) => {
                      const validTypes = [
                        'image/jpeg',
                        'image/jpg',
                        'image/png',
                      ];
                      if (!validTypes.includes(file.type)) {
                        message.error(
                          '仅支持上传 JPG、JPEG 或 PNG 格式的图片文件',
                        );
                        return false;
                      }

                      const isLt5M = file.size / 1024 / 1024 < 5;
                      if (!isLt5M) {
                        message.error('图片大小不能超过 5MB');
                        return false;
                      }

                      return true;
                    }}
                    customRequest={({ file, onSuccess }) => {
                      uploadImages([file as File]).then(() => {
                        onSuccess?.('ok');
                      });
                    }}
                    disabled={uploading || pasteUploading || !hasValidModel()}
                    className='w-full'
                    style={{
                      border: '2px dashed #d9d9d9',
                      borderRadius: '6px',
                      backgroundColor: '#fafafa',
                      padding: '16px',
                      textAlign: 'center',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      minHeight: '120px',
                    }}>
                    <p className='ant-upload-drag-icon'>
                      <CloudUploadOutlined
                        style={{
                          fontSize: '32px',
                          color: '#40a9ff',
                          transition: 'all 0.3s ease',
                        }}
                      />
                    </p>
                    <p className='ant-upload-text text-sm font-medium mb-1'>
                      {uploading || pasteUploading
                        ? t('上传中...')
                        : t('点击上传或将图片拖拽到此处')}
                    </p>
                    <p className='ant-upload-hint text-xs text-gray-500'>
                      {t('支持 JPG、JPEG、PNG 格式，单个文件不超过 5MB')}
                    </p>
                  </Upload.Dragger>
                </div>
              </div>
            )}

            <Sender
              value={inputMessage}
              onChange={setInputMessage}
              loading={loading}
              prefix={<ImageUploadButton />}
              onSubmit={handleSubmit}
              onCancel={handleCancel}
              placeholder={
                models.length === 2 && !hasValidModel()
                  ? t('请确保两个模型都已选择...')
                  : t('请先选择模型...')
              }
              disabled={!hasValidModel()}
            />
          </div>
          <div className='text-xs text-gray-500 text-center py-4'>
            {t('内容由AI生成，无法确保真实准确，仅供参考，并请遵守本平台')}
            <a
              className='text-blue-500 hover:underline underline-offset-2 mx-1'
              href=''>
              {t('《用户协议》')}
            </a>
            {t('及国家网络信息安全相关规定')}
          </div>
        </div>
      </div>
    </div>
  );
}
