import React, { useEffect, useRef, useCallback, useState } from 'react';

import { message, Typography, Image } from 'antd';
import {
  Bubble,
  BubbleProps,
  useXAgent,
  useXChat,
  // XStream,
} from '@ant-design/x';
import { UserOutlined, CopyOutlined } from '@ant-design/icons';
import { type GetProp } from 'antd';
import { AIResponseDisplay } from '@/components';
import { useAuthStore } from '@/store/features';

type AgentUserMessage = {
  role: 'user';
  content: string;
  images?: string[];
  sessionId?: string;
  onComplete?: () => void;
};

type AgentAIMessage = {
  role: 'assistant';
  content: string;
  onComplete?: () => void;
};

type AgentSystemMessage = {
  role: 'system';
  content: string;
  onComplete?: () => void;
};

export type AgentMessage =
  | AgentUserMessage
  | AgentAIMessage
  | AgentSystemMessage;

const renderMarkdown: BubbleProps['messageRender'] = (content: string) => {
  return (
    <Typography>
      <AIResponseDisplay response={content} />
    </Typography>
  );
};

type modelOptions = {
  temperature: number;
  topK: number;
  topP: number;
  maxTokens: number;
};

export interface ChatViewProps {
  modelState: {
    modelName: string;
    modelPath: string;
    taskUuid: string;
    modelConfigId: string;
    baseUuid: string;
    systemPrompt: string;
  } & modelOptions;

  requestChat: (
    modelState: ChatViewProps['modelState'],
    messages: any,
    requestSessionId?: string,
    requestImages?: string[],
  ) => {
    request: Promise<Response>;
    abort: AbortController;
  };
  uploadedImages: Array<{
    url: string;
    localUrl: string; // 本地 blob URL
    name: string;
    uid: string;
    fileName: string;
    file: File; // 保存原始文件对象
  }>;
  sessionId: string; // 添加 sessionId
  getImageByName: (imageName: string) => Promise<string>; // 全局图片获取函数
  onRequestRef?: (ref: (content: AgentMessage) => void) => void;
  onCancelRef?: (ref: () => void) => void;
  onClearRef?: (ref: () => void) => void;
}

type OnUpdate = (message: any) => void;

const ChatView = React.memo(
  ({
    modelState,
    uploadedImages,
    sessionId,
    getImageByName,
    requestChat,
    onRequestRef,
    onCancelRef,
    onClearRef,
  }: ChatViewProps) => {
    const abortControllerRef = useRef<AbortController | null>(null);
    const readerRef = useRef<ReadableStreamDefaultReader | null>(null);
    const modelStateRef = useRef(modelState);

    const { user } = useAuthStore();

    const roles: GetProp<typeof Bubble.List, 'roles'> = {
      user: {
        placement: 'end',
        avatar: {
          icon: <img src={user.headerImg} alt='avatar' />,
          style: { background: '#87d068' },
        },
      },
      assistant: {
        placement: 'start',
        avatar: { icon: <UserOutlined />, style: { background: '#fde3cf' } },
      },
    };

    useEffect(() => {
      modelStateRef.current = modelState;
    }, [modelState]);

    const handleCopy = (content: string) => {
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard
          .writeText(content)
          .then(() => {
            message.success(t('复制成功'));
          })
          .catch(() => {
            message.error(t('复制失败'));
          });
      } else {
        // Fallback for non-secure contexts
        const textArea = document.createElement('textarea');
        textArea.value = content;
        textArea.style.position = 'absolute';
        textArea.style.left = '-9999px';
        document.body.appendChild(textArea);
        textArea.select();
        try {
          document.execCommand('copy');
          message.success(t('复制成功'));
        } catch (err) {
          message.error(t('复制失败'));
        } finally {
          document.body.removeChild(textArea);
        }
      }
    };

    const processLines = (
      lines: string[],
      onUpdate: OnUpdate,
      accumulate: (msg: string) => void,
      fullMessage: string,
    ) => {
      for (const line of lines) {
        try {
          if (line.startsWith('data: ')) {
            const jsonStr = line.substring(6);
            const parsed = JSON.parse(jsonStr);
            if (parsed.text) {
              accumulate(parsed.text);
              onUpdate({ role: 'assistant', content: fullMessage });
            }
          }
        } catch (e) {
          console.error('Failed to parse chunk:', e);
        }
      }
    };

    const processBuffer = (
      buffer: string,
      onUpdate: OnUpdate,
      accumulate: (msg: string) => void,
      fullMessage: string,
    ) => {
      try {
        if (buffer.startsWith('data: ')) {
          const jsonStr = buffer.substring(6);
          const parsed = JSON.parse(jsonStr);
          if (parsed.text) {
            accumulate(parsed.text);
            onUpdate({ role: 'assistant', content: fullMessage });
          }
        }
      } catch (e) {
        console.error('Failed to parse final buffer:', e);
      }
    };

    const [agent] = useXAgent<any>({
      request: async ({ messages }, { onSuccess, onUpdate }) => {
        const onComplete = messages?.[messages.length - 1]?.onComplete;
        const lastMessage = messages?.[messages.length - 1];
        const sessionId = lastMessage?.sessionId;
        const images = lastMessage?.images;

        try {
          const { request, abort } = requestChat(
            modelStateRef.current,
            messages,
            sessionId,
            images,
          );
          abortControllerRef.current = abort;
          const response = await request;

          if (!response.ok) {
            const errorBody = await response.text(); // 或者使用 response.json() 如果返回的是 JSON 数据
            throw new Error(errorBody);
          }

          const reader = response.body?.getReader();
          if (reader) {
            readerRef.current = reader;
          }
          const decoder = new TextDecoder();
          let fullMessage = '';
          let buffer = '';
          const cancelRequest = () => {
            if (readerRef.current) {
              readerRef.current.cancel();
              readerRef.current = null;
            }
            if (abortControllerRef.current) {
              abortControllerRef.current.abort();
              abortControllerRef.current = null;
            }
          };

          if (onCancelRef) {
            onCancelRef(cancelRequest);
          }

          try {
            while (reader) {
              const { value, done } = await reader.read();
              if (done) {
                if (buffer) {
                  processBuffer(
                    buffer,
                    onUpdate,
                    (msg) => (fullMessage += msg),
                    fullMessage,
                  );
                  buffer = '';
                }
                onSuccess({ role: 'assistant', content: fullMessage } as any);
                onComplete?.();
                break;
              }

              const chunk = decoder.decode(value, { stream: true });
              buffer += chunk;

              const lines = buffer.split('\n');
              buffer = lines.pop() || '';

              processLines(
                lines,
                onUpdate,
                (msg) => (fullMessage += msg),
                fullMessage,
              );
            }
          } catch (error) {
            onSuccess({
              role: 'assistant',
              content: t('服务器发生错误，请稍后重试'),
            } as any);
            onComplete?.();
            throw error;
          } finally {
            reader?.releaseLock();
            readerRef.current = null;
          }
        } catch (error: any) {
          if (error.name === 'AbortError') {
            onSuccess({ role: 'assistant', content: '请求已取消' } as any); // 取消时给出提示
          } else {
            message.error(error.message); // 错误时给出提示
            setMessages(
              messages
                // ?.slice(0, messages.length-1)
                ?.map((item, index) => {
                  return {
                    message: {
                      role: item.role,
                      content: item.content,
                    },
                    status: 'success',
                    id: index,
                  };
                }) ?? [],
            );
          }
          onComplete?.(); // 确保任何情况下都调用完成回调
        }
      },
    });

    useEffect(() => {
      return () => {
        if (readerRef.current) {
          readerRef.current.cancel();
          readerRef.current = null;
        }
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
          abortControllerRef.current = null;
        }
      };
    }, []);

    const {
      onRequest,
      messages: xMessage,
      setMessages,
    } = useXChat<any, any>({
      agent,
      requestPlaceholder: {
        role: 'assistant',
        content: 'Wating...',
      },
    });

    // 异步图片组件 - 使用全局缓存，避免重复请求
    const AsyncChatImage = React.memo(({ imageName }: { imageName: string }) => {
      const [imageSrc, setImageSrc] = useState<string>('');
      const [loading, setLoading] = useState(true);
      const [error, setError] = useState(false);

      // 使用稳定的依赖项，只在 imageName 变化时重新加载
      useEffect(() => {
        let isMounted = true;
        
        const loadImage = async () => {
          setLoading(true);
          setError(false);
          
          try {
            // 使用全局的 getImageByName 函数，它包含缓存逻辑
            const url = await getImageByName(imageName);
            if (isMounted) {
              setImageSrc(url);
              setError(!url);
            }
          } catch (error) {
            console.error('Failed to load chat image:', error);
            if (isMounted) {
              setError(true);
              setImageSrc('');
            }
          } finally {
            if (isMounted) {
              setLoading(false);
            }
          }
        };

        loadImage();
        
        return () => {
          isMounted = false;
        };
      }, [imageName]); // 只依赖 imageName，不依赖其他变量

      if (loading) {
        return (
          <div className='w-50 h-50 bg-gray-200 rounded-lg flex items-center justify-center'>
            <span className='text-sm text-gray-500'>加载中...</span>
          </div>
        );
      }

      if (error || !imageSrc) {
        return (
          <div className='w-50 h-50 bg-gray-200 rounded-lg flex items-center justify-center'>
            <span className='text-sm text-gray-500'>加载失败</span>
          </div>
        );
      }

      return (
        <Image
          src={imageSrc}
          alt={`上传的图片`}
          width={200}
          height={200}
          className='object-cover rounded-lg border border-gray-200'
          style={{
            maxWidth: '200px',
            maxHeight: '200px',
            objectFit: 'cover',
          }}
          preview={{
            src: imageSrc,
            mask: '预览',
          }}
          fallback='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN'
        />
      );
    });

    const handleClear = useCallback(() => {
      setMessages([
        {
          id: Date.now(),
          message: { role: 'system', content: modelState.systemPrompt },
          status: 'success',
        },
      ]);
    }, [modelState.systemPrompt]);

    useEffect(() => {
      if (onClearRef) {
        onClearRef(handleClear);
      }
    }, [onClearRef, handleClear]);

    React.useEffect(() => {
      if (onRequestRef) {
        onRequestRef(onRequest);
      }
    }, [onRequestRef, onRequest]);

    React.useEffect(() => {
      setMessages((messages) => {
        const newMessages = messages.filter(
          (msg) => msg.message.role !== 'system',
        );
        return [
          {
            id: Date.now(),
            message: { role: 'system', content: modelState.systemPrompt },
            status: 'success',
          },
          ...newMessages,
        ];
      });
    }, [modelState.systemPrompt]);

    return (
      <div className='h-full flex flex-col gap-4 p-4 w-full border-r-1  border-zinc-200 overflow-auto'>
        <div className=' w-full'>
          <span className='bg-[#e5e7eb] text-lg rounded-md px-4 py-2'>
            {modelState.modelName}
          </span>
        </div>
        <Bubble.List
          roles={roles}
          items={xMessage
            .filter((item) => item.message.role !== 'system')
            .map(({ id, message }) => {
              // 过滤掉不应该传递到DOM的属性
              const { sessionId, onComplete, images, ...domSafeMessage } =
                message as any;

              return {
                footer:
                  message.role === 'assistant' ? (
                    <div className='flex gap-2 ml-2'>
                      <div
                        className='hover:bg-gray-300 rounded-sm cursor-pointer active:bg-gray-200'
                        onClick={() => {
                          handleCopy(message.content);
                        }}>
                        <CopyOutlined />
                      </div>
                    </div>
                  ) : null,
                key: id,
                messageRender: (content: string) => (
                  <div>
                    {/* 渲染图片（如果有） */}
                    {images && images.length > 0 && (
                      <div className='mb-2 flex flex-wrap gap-2'>
                        {images.map(
                          (imageUrl: { data: string }, index: number) => (
                            <AsyncChatImage
                              key={index}
                              imageName={imageUrl.data}
                            />
                          ),
                        )}
                      </div>
                    )}
                    {/* 渲染文本内容 */}
                    {renderMarkdown(content)}
                  </div>
                ),
                loading: message.content === 'Wating...',
                ...domSafeMessage,
              };
            })}
        />
      </div>
    );
  },
);

export default ChatView;
