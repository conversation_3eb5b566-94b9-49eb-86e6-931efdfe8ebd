import request, { returnData } from '@/services';
import { ModelItem } from './types';

export const getModels = (): Promise<returnData<ModelItem[]>> => {
  return request.get('/api/model/getModelsByCoId');
};

export const getOllamaModels = (): Promise<returnData<any[]>> => {
  return fetch('/ai/api/tags').then((res) => res.json());
};

export const getModelInfoListForSelect = (type: string) => {
  return request.get('/api/modelConfig/getModelInfoListForSelect', {
    params: {
      modelType: type,
    },
  });
};

export const getKnowledgeBaseListForSelect = () => {
  return request.get('/api/knowledge/getKnowledgeBaseListForSelect');
};

export const ollamaChat = () => {};

/**
 * 创建新的会话ID
 * @returns 会话创建结果
 */
export const createSession = (): Promise<returnData<{ result: string }>> => {
  return request.post('/api/knowledge/createSession');
};

/**
 * 删除会话
 * @param sessionId 会话ID
 * @returns 删除结果
 */
export const deleteSession = (sessionId: string): Promise<returnData<any>> => {
  return request.post('/api/knowledge/deleteSession', {
    sessionId,
  });
};

/**
 * 上传图片到深度提示接口
 * @param sessionId 会话ID
 * @param images 图片文件数组
 * @returns 上传结果
 */
export const uploadImagesForDeepPrompt = (
  sessionId: string,
  images: File[],
): Promise<returnData<any[]>> => {
  const formData = new FormData();
  formData.append('sessionId', sessionId);

  images.forEach((file) => {
    formData.append('images', file);
  });

  return request.post('/api/file/uploadFileForDeepPrompt', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
};

/**
 * 获取单个原始图片
 * @param sessionId 会话ID
 * @param imageId 图片ID
 * @returns 图片获取结果
 */
export const getPromptSingleOriginalImage = (
  sessionId: string,
  imageId: string,
): Promise<returnData<{ result: string }>> => {
  return request.get('/api/knowledge/getPromptSingleOriginalImage', {
    params: {
      sessionId,
      imageId,
    },
  });
};
