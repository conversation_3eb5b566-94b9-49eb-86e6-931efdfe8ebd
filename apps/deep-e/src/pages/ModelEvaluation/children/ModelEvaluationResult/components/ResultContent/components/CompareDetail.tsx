'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Image } from 'antd';
import { CollapsibleText } from './CollapsibleText';
import { ModelResponse } from '../../../../../type';
import * as _ from 'lodash';

interface ComparisonDetailProps {
  comparison: ModelResponse;
}

// Mock data for images - will be replaced with real API data later
const mockImages = [
  {
    id: '1',
    url: 'https://via.placeholder.com/150x150/4285F4/FFFFFF?text=Image+1',
    alt: 'Mock Image 1',
  },
  {
    id: '2', 
    url: 'https://via.placeholder.com/150x150/34A853/FFFFFF?text=Image+2',
    alt: 'Mock Image 2',
  },
  {
    id: '3',
    url: 'https://via.placeholder.com/150x150/EA4335/FFFFFF?text=Image+3', 
    alt: 'Mock Image 3',
  },
];

export function ComparisonDetail({ comparison }: ComparisonDetailProps) {
  const [viewMode, setViewMode] = useState<'split' | 'tabbed'>('split');

  return (
    <div className='p-6'>
      <div className='flex justify-between items-center mb-6'>
        <div>
          <h2 className='text-2xl font-bold'>{t('详细信息')}</h2>
          {/* <div className='text-sm text-muted-foreground mt-1'>
            {formatDate(new Date(comparison.timestamp))} • {comparison.category}
          </div> */}
        </div>

        <div className='flex gap-2'>
          <Badge
            variant={viewMode === 'split' ? 'default' : 'outline'}
            className='cursor-pointer'
            onClick={() => setViewMode('split')}>
            {t('分栏视图')}
          </Badge>
          <Badge
            variant={viewMode === 'tabbed' ? 'default' : 'outline'}
            className='cursor-pointer'
            onClick={() => setViewMode('tabbed')}>
            {t('选项卡视图')}
          </Badge>
        </div>
      </div>

      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6'>
        {/* Question Section */}
        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-lg'>{t('问题')}</CardTitle>
          </CardHeader>
          <CardContent>
            <CollapsibleText
              text={comparison.candidateUserPrompt}
              maxLength={300}
            />
          </CardContent>
        </Card>

        {/* Images Section */}
        <Card>
          <CardHeader className='pb-2'>
            <CardTitle className='text-lg'>{t('图片')}</CardTitle>
          </CardHeader>
          <CardContent>
            {mockImages.length > 0 ? (
              <div className='flex flex-wrap gap-3'>
                {mockImages.map((image) => (
                  <div key={image.id} className='flex-shrink-0'>
                    <Image
                      src={image.url}
                      alt={image.alt}
                      width={100}
                      height={100}
                      className='object-cover rounded-lg border border-gray-200'
                      style={{
                        width: '100px',
                        height: '100px',
                      }}
                      preview={{
                        mask: t('预览'),
                      }}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className='text-gray-500 text-sm'>{t('暂无图片')}</div>
            )}
          </CardContent>
        </Card>
      </div>

      {viewMode === 'tabbed' ? (
        <TabbedView comparison={comparison} />
      ) : (
        <SplitView comparison={comparison} />
      )}
    </div>
  );
}

function TabbedView({ comparison }: { comparison: ModelResponse }) {
  return (
    <Tabs defaultValue='modelA' className='w-full'>
      <TabsList
        className={`grid grid-cols-${comparison.evalType === 0 ? 3 : comparison.evalType === 3 ? 1 : 2}`}>
        <TabsTrigger value='modelA'>{t('模型A')}</TabsTrigger>
        {(comparison.evalType === 0 || comparison.evalType === 1) && (
          <TabsTrigger value='modelB'>{t('模型B')}</TabsTrigger>
        )}

        {(comparison.evalType === 0 || comparison.evalType === 3) && (
          <TabsTrigger value='referee'>{t('裁判模型')}</TabsTrigger>
        )}
      </TabsList>

      <TabsContent value='modelA'>
        <ModelOutputCard
          title={t('模型A输出')}
          output={comparison.responseA}
          score={3}
          //   isWinner={(comparison.judgeResponse?.better || '') === 'A'}
        />
      </TabsContent>

      {(comparison.evalType === 0 || comparison.evalType === 1) && (
        <TabsContent value='modelB'>
          <ModelOutputCard
            title={t('模型B输出')}
            output={comparison.responseB}
            score={3}
            // isWinner={
            //   !_.isNil(comparison.judgeResponse)
            //     ? comparison.judgeResponse.better === 'B' ||
            //       comparison.judgeResponse.better === 'both'
            //     : false
            // }
          />
        </TabsContent>
      )}

      {!_.isNil(comparison.judgeResponse) && (
        <TabsContent value='referee'>
          <Card>
            <CardHeader className='pb-2'>
              <CardTitle className='text-lg'>{t('裁判模型评估')}</CardTitle>
            </CardHeader>
            <CardContent>
              <CollapsibleText
                text={comparison.judgeResponse}
                maxLength={300}
              />
              {/* <div className='mt-4 p-3 bg-muted rounded-md'>
                <div className='font-medium'>最终结果</div>
                <div className='mt-1'>
                  胜出者:
                  <Badge>
                    {comparison.judgeResponse.better === 'A'
                      ? '模型A'
                      : comparison.judgeResponse.better === 'B'
                        ? '模型B'
                        : '平局'}
                  </Badge>
                </div>
              </div> */}
            </CardContent>
          </Card>
        </TabsContent>
      )}
    </Tabs>
  );
}

function SplitView({ comparison }: { comparison: ModelResponse }) {
  return (
    <div className='grid grid-cols-2 gap-6'>
      <ModelOutputCard
        title={t('模型A输出')}
        output={comparison.responseA}
        score={3}
        // isWinner={(comparison.judgeResponse?.better || '') === 'A'}
      />
      {(comparison.evalType === 0 || comparison.evalType === 1) && (
        <ModelOutputCard
          title={t('模型B输出')}
          output={comparison.responseB}
          score={3}
          // isWinner={(comparison.judgeResponse?.better || '') === 'B'}
        />
      )}

      {!_.isNil(comparison.judgeResponse) && (
        <Card className='col-span-2'>
          <CardHeader className='pb-2'>
            <CardTitle className='text-lg'>{t('裁判模型评估')}</CardTitle>
          </CardHeader>
          <CardContent>
            <CollapsibleText text={comparison.judgeResponse} maxLength={300} />
            {/* <div className='mt-4 p-3 bg-muted rounded-md'>
              <div className='font-medium'>最终结果</div>
              <div className='mt-1'>
                胜出者:
                <Badge>
                  {comparison.judgeResponse.better === 'A'
                    ? '模型A'
                    : comparison.judgeResponse.better === 'B'
                      ? '模型B'
                      : '平局'}
                </Badge>
              </div>
            </div> */}
          </CardContent>
        </Card>
      )}
    </div>
  );
}

function ModelOutputCard({
  title,
  output,
  score,
  //   isWinner,
}: {
  title: string;
  output: string;
  score: number;
  //   isWinner: boolean;
}) {
  return (
    <Card>
      <CardHeader className='pb-2 flex flex-row items-center justify-between'>
        <CardTitle className='text-lg'>{title}</CardTitle>
        <div className='flex items-center gap-2'>
          <div className='flex'>
            {Array.from({ length: 5 }).map((_, i) => (
              <span
                key={i}
                className={`text-sm ${i < score ? 'text-yellow-500' : 'text-gray-300'}`}>
                ★
              </span>
            ))}
          </div>
          {/* {isWinner && <Badge>{t('胜出')}</Badge>} */}
        </div>
      </CardHeader>
      <CardContent>
        <CollapsibleText text={output} maxLength={300} />
      </CardContent>
    </Card>
  );
}
