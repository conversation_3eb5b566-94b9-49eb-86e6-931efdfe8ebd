import React from 'react';
import { Radio, Space } from 'antd';
import type { RadioChangeEvent } from 'antd';

interface TabCardOption {
  label: string;
  value: any;
  description?: string;
}

interface TabCardProps {
  options: TabCardOption[];
  value: string;
  onChange: (value: string) => void;
  title?: string;
  description?: string;
}

export default function TabCard({
  value,
  onChange,
  options,
  title,
  description,
}: TabCardProps) {
  const handleChange = (e: RadioChangeEvent) => {
    onChange(e.target.value);
  };

  return (
    <div title={title} className='my-6'>
      {description && <p className='text-gray-500 mb-4'>{description}</p>}
      <Radio.Group
        value={value}
        onChange={handleChange}
        style={{ width: '100%' }}>
        <Space
          direction='horizontal'
          size='middle'
          style={{ display: 'flex', flexWrap: 'wrap', gap: '16px' }}>
          {options.map((opt) => (
            <div
              key={opt.value}
              className={`border rounded-xl p-4 flex-1 min-w-[300px] border-gray-300 ${
                value === opt.value ? 'bg-gray-100' : ''
              }`}>
              <Radio value={opt.value}>
                <div>
                  <div className='font-medium'>{opt.label}</div>
                  {opt.description && (
                    <div className='text-sm text-gray-500 mt-1'>
                      {opt.description}
                    </div>
                  )}
                </div>
              </Radio>
            </div>
          ))}
        </Space>
      </Radio.Group>
    </div>
  );
}
