import React, { useState, useEffect, useRef } from 'react';
import { Form, Input, Button, App, Radio, Tree } from 'antd';
import {
  FolderOutlined,
  FolderOpenOutlined,
  CloseOutlined,
  DownloadOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import {
  uploadFolderForDataSet,
  saveDataSetInfoForCompatibility,
} from '@/pages/Datasets/api';
import { t } from '@/languages';

// Uppy imports
import Uppy from '@uppy/core';

// JSZip import for folder compression
import JSZip from 'jszip';

interface DatasetFormValues {
  datasetName: string;
  datasetDesc: string;
}

interface MultiModalUploadProps {
  form: any;
}

export default function MultiModalUpload({ form }: MultiModalUploadProps) {
  const message = App.useApp().message;
  const navigate = useNavigate();
  const [jsonlFiles, setJsonlFiles] = useState<string[]>([]);
  const [selectedJsonl, setSelectedJsonl] = useState<string | undefined>(
    undefined,
  );
  const [showJsonlSelectionSection, setShowJsonlSelectionSection] =
    useState(false);
  const [fileTreeData, setFileTreeData] = useState<any[]>([]);
  const [uppyInstance, setUppyInstance] = useState<any>(null);
  const folderInputRef = useRef<HTMLInputElement>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);
  const [compressedZipBlob, setCompressedZipBlob] = useState<Blob | null>(null);
  const [zipFileName, setZipFileName] = useState<string>('');
  const [fileUuid, setFileUuid] = useState<string>('');
  const [uploading, setUploading] = useState<boolean>(false);
  const [uploadedZipName, setUploadedZipName] = useState<string>('');

  // 下载压缩后的zip文件
  const handleDownloadZip = () => {
    if (!compressedZipBlob || !zipFileName) {
      message.error(t('没有可下载的压缩文件'));
      return;
    }

    const url = URL.createObjectURL(compressedZipBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = zipFileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    message.success(t('zip文件下载成功'));
  };

  // 上传压缩后的zip文件到临时存储
  const uploadCompressedFile = async (zipBlob?: Blob, fileName?: string) => {
    const blob = zipBlob || compressedZipBlob;
    const name = fileName || zipFileName;

    if (!blob || !name) {
      message.error(t('请先压缩文件夹'));
      return;
    }

    setUploading(true);
    const formData = new FormData();
    const zipFile = new File([blob], name, {
      type: 'application/zip',
    });
    formData.append('file', zipFile);

    try {
      const res = await uploadFolderForDataSet(formData);
      if (res.code === 0 && res.data.result) {
        setFileUuid(res.data.result);
        setUploadedZipName(name);
        message.success(t('压缩文件上传成功'));
      } else {
        message.error(t(`压缩文件上传失败: ${res.msg || ''}`));
      }
    } catch (error) {
      console.error(t('上传压缩文件时发生错误:'), error);
      message.error(t('上传压缩文件时发生错误'));
    } finally {
      setUploading(false);
    }
  };

  // 自动压缩并上传文件夹
  const autoCompressAndUpload = async (uppyInstance: any) => {
    // const datasetName = form.getFieldValue('datasetName');
    // if (!datasetName) {
    //   message.error(t('请先输入数据集名称'));
    //   return;
    // }

    try {
      const zip = new JSZip();
      const allFiles = uppyInstance.getFiles();

      message.loading(t('正在压缩文件夹...'), 0);

      for (const file of allFiles) {
        const relativePath = (file.meta as any)?.relativePath || file.name;
        console.log('添加文件到zip:', relativePath);

        const fileData = await new Promise<ArrayBuffer>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as ArrayBuffer);
          reader.onerror = reject;
          reader.readAsArrayBuffer(file.data as File);
        });

        zip.file(relativePath, fileData);
      }

      console.log('生成zip文件...');
      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: {
          level: 6,
        },
      });

      const fileName = `file.zip`;
      setCompressedZipBlob(zipBlob);
      setZipFileName(fileName);

      message.destroy();
      message.success(t('文件夹压缩完成，开始上传...'));

      // 压缩完成后直接上传
      await uploadCompressedFile(zipBlob, fileName);
    } catch (error) {
      message.destroy();
      console.error('自动压缩和上传失败:', error);
      message.error(t('自动压缩和上传失败，请重试'));
    }
  };

  // 执行JSONL文件检测和文件树构建的核心逻辑
  const processAllFiles = async (uppyInstance: any) => {
    console.log('processAllFiles called');
    const allFiles = uppyInstance.getFiles();
    console.log('Processing all files:', allFiles.length);

    if (allFiles.length === 0) {
      console.log('No files found, resetting states');
      setJsonlFiles([]);
      setSelectedJsonl(undefined);
      setShowJsonlSelectionSection(false);
      setFileTreeData([]);
      setCompressedZipBlob(null);
      setZipFileName('');
      setFileUuid('');
      setUploadedZipName('');
      return;
    }

    // 检查JSONL文件
    const jsonlFiles = allFiles
      .filter(
        (file: any) => file.name && file.name.toLowerCase().endsWith('.jsonl'),
      )
      .map((file: any) => file.name!);

    console.log('Found JSONL files:', jsonlFiles);

    if (jsonlFiles.length > 0) {
      setJsonlFiles(jsonlFiles);
      if (jsonlFiles.length === 1) {
        setSelectedJsonl(jsonlFiles[0]);
      } else {
        setSelectedJsonl(undefined);
      }
      setShowJsonlSelectionSection(true);
      message.success(
        t(`发现 ${jsonlFiles.length} 个 JSONL 文件，开始自动压缩和上传...`),
      );

      // 自动压缩并上传
      await autoCompressAndUpload(uppyInstance);
    } else {
      setJsonlFiles([]);
      setSelectedJsonl(undefined);
      setShowJsonlSelectionSection(false);
      message.warning(t('选择的文件夹中未找到 .jsonl 文件'));
    }

    // 构建文件树
    const treeData = buildFileTree(allFiles);
    setFileTreeData(treeData);
  };

  // 防抖的文件处理函数
  const debouncedProcessFiles = (uppyInstance: any) => {
    console.log('debouncedProcessFiles called');
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
      console.log('Cleared previous debounce timer');
    }

    debounceTimerRef.current = setTimeout(async () => {
      console.log('Debounce timer fired, processing files');
      await processAllFiles(uppyInstance);
    }, 500);
    console.log('Set new debounce timer');
  };

  // 取消整个文件夹上传的处理函数
  const handleCancelUpload = () => {
    if (!uppyInstance) return;

    const allFiles = uppyInstance.getFiles();
    const fileCount = allFiles.length;

    uppyInstance.cancelAll();

    setJsonlFiles([]);
    setSelectedJsonl(undefined);
    setShowJsonlSelectionSection(false);
    setFileTreeData([]);
    setCompressedZipBlob(null);
    setZipFileName('');
    setFileUuid('');
    setUploadedZipName('');

    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    if (folderInputRef.current) {
      folderInputRef.current.value = '';
    }

    console.log('Cancel upload completed, all states reset');
    message.success(t(`已取消上传，清除了 ${fileCount} 个文件`));
  };

  // 处理文件夹选择
  const handleFolderSelect = () => {
    console.log('handleFolderSelect called');
    if (folderInputRef.current) {
      console.log('Clicking folder input');
      folderInputRef.current.click();
    } else {
      console.error('folderInputRef.current is null');
    }
  };

  // 处理文件夹input变化
  const handleFolderInputChange = (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const files = event.target.files;
    console.log('handleFolderInputChange triggered, files:', files?.length);

    if (files && files.length > 0 && uppyInstance) {
      console.log('Selected folder files:', files.length, 'files');

      uppyInstance.cancelAll();
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      Array.from(files).forEach((file) => {
        try {
          uppyInstance.addFile({
            name: file.name,
            type: file.type,
            data: file,
            meta: {
              relativePath: (file as any).webkitRelativePath || file.name,
            },
          });
        } catch (error) {
          console.error('Error adding file to Uppy:', error);
        }
      });

      debouncedProcessFiles(uppyInstance);
    }

    event.target.value = '';
  };

  // 初始化Uppy实例
  useEffect(() => {
    const uppy = new Uppy({
      id: 'multimodal-dataset-uploader',
      autoProceed: false,
      allowMultipleUploads: false,
      restrictions: {
        maxNumberOfFiles: null,
        allowedFileTypes: null,
      },
      meta: {
        allowFolderUpload: true,
      },
    });

    uppy.on('files-added', (files) => {
      console.log('Files added event triggered, files count:', files.length);
      debouncedProcessFiles(uppy);
    });

    uppy.on('file-removed', () => {
      console.log('File removed, re-processing remaining files');
      debouncedProcessFiles(uppy);
    });

    setUppyInstance(uppy);

    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
      uppy.destroy();
    };
  }, []);

  // 修改buildFileTree函数以支持Uppy文件对象
  const buildFileTree = (files: any[]) => {
    const tree: any = {};

    files.forEach((file) => {
      const pathParts = (file.meta as any)?.relativePath
        ? (file.meta as any).relativePath.split('/')
        : file.name.split('/');
      let current = tree;

      pathParts.forEach((part: string, index: number) => {
        if (!current[part]) {
          current[part] = {
            title: part,
            key: pathParts.slice(0, index + 1).join('/'),
            isLeaf: index === pathParts.length - 1,
            children: {},
            file: index === pathParts.length - 1 ? file : null,
          };
        }
        current = current[part].children;
      });
    });

    const convertToTreeData = (obj: any): any[] => {
      return Object.values(obj).map((item: any) => ({
        title: item.title,
        key: item.key,
        isLeaf: item.isLeaf,
        children: item.isLeaf ? undefined : convertToTreeData(item.children),
        icon: item.isLeaf ? undefined : <FolderOutlined />,
      }));
    };

    return convertToTreeData(tree);
  };

  const handleSubmit = async (values: DatasetFormValues) => {
    console.log('🚀 MultiModalUpload handleSubmit called with values:', values);
    console.log('🚀 Current form values:', form.getFieldsValue());
    console.log('🚀 Selected JSONL:', selectedJsonl);
    console.log('🚀 File UUID:', fileUuid);

    if (!fileUuid) {
      message.error(t('请先上传压缩文件'));
      return;
    }

    if (!selectedJsonl) {
      message.error(t('请先选择JSONL文件'));
      return;
    }

    const params = {
      fileUuid: fileUuid,
      datasetName: values.datasetName,
      datasetDesc: values.datasetDesc,
      datasetType: 2, // 多模态固定为2
      jsonFileName: selectedJsonl, // 选择的JSONL文件名
    };

    try {
      const res = await saveDataSetInfoForCompatibility(params);
      if (res.code === 0) {
        message.success(t('多模态数据集创建成功'));
        navigate('/datasets');
      } else {
        message.error(t(`数据集创建失败: ${res.msg || ''}`));
      }
    } catch (error) {
      console.error(t('创建数据集时发生错误:'), error);
      message.error(t('创建数据集时发生错误'));
    }
  };

  return (
    <div className='max-w-[600px]'>
      <Form
        form={form}
        onFinish={(values) => {
          console.log('📝 Form onFinish triggered with values:', values);
          handleSubmit(values);
        }}
        onFinishFailed={(errorInfo) => {
          console.log('❌ Form onFinishFailed:', errorInfo);
        }}
        layout='vertical'>
        <Form.Item
          label={t('数据集名称')}
          name='datasetName'
          rules={[{ required: true, message: t('请输入数据集名称') }]}>
          <Input />
        </Form.Item>
        <Form.Item
          label={t('数据集描述')}
          name='datasetDesc'
          rules={[{ required: true, message: t('请输入数据集描述') }]}>
          <Input.TextArea rows={4} />
        </Form.Item>
        <Form.Item label={t('上传文件夹')}>
          <div className='border border-dashed border-gray-300 rounded-lg p-4'>
            <div className='text-center'>
              <FolderOutlined className='text-6xl text-gray-400 mb-4' />
              <p className='text-lg font-medium text-gray-700 mb-2'>
                {t('选择多模态数据集文件夹')}
              </p>
              <p className='text-sm text-gray-500 mb-4'>
                {t('点击下方按钮选择包含多模态数据的完整文件夹')}
              </p>
              <p className='text-xs text-blue-600 mb-2'>
                {t('💡 提示：请确保文件夹中包含 .jsonl 文件')}
              </p>
              <p className='text-xs text-orange-600 mb-6'>
                {t('📦 文件夹将自动压缩为 .zip 文件后上传')}
              </p>

              <input
                ref={folderInputRef}
                type='file'
                {...({ webkitdirectory: '' } as any)}
                multiple
                style={{ display: 'none' }}
                onChange={handleFolderInputChange}
              />

              {fileTreeData.length === 0 ? (
                <Button
                  type='primary'
                  icon={<FolderOpenOutlined />}
                  size='large'
                  onClick={handleFolderSelect}>
                  {t('选择文件夹')}
                </Button>
              ) : (
                <div className='space-y-2'>
                  <div className='text-sm text-green-600 font-medium'>
                    {t('已选择文件夹，共 {count} 个文件', {
                      count: uppyInstance ? uppyInstance.getFiles().length : 0,
                    })}
                  </div>
                  <Button
                    type='default'
                    icon={<CloseOutlined />}
                    size='large'
                    onClick={handleCancelUpload}
                    className='border-red-300 text-red-600 hover:border-red-500 hover:text-red-700'>
                    {t('取消上传')}
                  </Button>
                </div>
              )}
            </div>
          </div>

          {fileTreeData.length > 0 && (
            <div className='mt-6 p-4 border border-blue-200 rounded-lg bg-blue-50'>
              <div className='flex items-center justify-between mb-3'>
                <div className='text-sm font-medium text-blue-800'>
                  {t('已选择的文件夹结构')}
                </div>
                <div className='text-xs text-blue-600'>
                  {t('如需重新选择，请点击上方"取消上传"按钮')}
                </div>
              </div>
              <Tree
                treeData={fileTreeData}
                defaultExpandAll
                showIcon
                className='bg-white p-3 rounded border border-gray-200'
                blockNode
              />
            </div>
          )}
        </Form.Item>

        {showJsonlSelectionSection && jsonlFiles.length > 0 && (
          <>
            <Form.Item label={t('选择展示的 JSONL 文件')} required>
              <div className='border border-gray-200 rounded-md p-4 bg-gray-50'>
                <p className='text-sm text-gray-600 mb-3'>
                  {t(
                    '在上传的文件夹中找到以下 JSONL 文件，请选择一个作为数据集的展示文件：',
                  )}
                </p>
                <Radio.Group
                  onChange={(e) => setSelectedJsonl(e.target.value)}
                  value={selectedJsonl}
                  className='w-full'>
                  <div className='space-y-2'>
                    {jsonlFiles.map((file, index) => (
                      <div
                        key={file}
                        className={`flex items-center p-3 rounded border ${index !== jsonlFiles.length - 1 ? 'border-b border-gray-100' : ''}`}>
                        <Radio value={file}>
                          <span className='ml-2 text-sm font-medium'>
                            {file}
                          </span>
                        </Radio>
                      </div>
                    ))}
                  </div>
                </Radio.Group>
              </div>
            </Form.Item>
          </>
        )}

        {showJsonlSelectionSection && (
          <Form.Item label={t('上传状态')}>
            <div className='border border-gray-200 rounded-md p-4 bg-gray-50'>
              <div className='flex flex-wrap gap-3 items-center'>
                {uploading && (
                  <div className='flex items-center text-sm text-blue-600'>
                    <span>🔄 {t('正在压缩和上传文件...')}</span>
                  </div>
                )}

                {compressedZipBlob && (
                  <Button
                    type='default'
                    icon={<DownloadOutlined />}
                    size='large'
                    onClick={handleDownloadZip}>
                    {t('下载 {fileName}', { fileName: zipFileName })}
                  </Button>
                )}

                {fileUuid && uploadedZipName && (
                  <div className='flex items-center text-sm text-green-600'>
                    <span>
                      ✅ {t('压缩文件已上传')}: {uploadedZipName}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </Form.Item>
        )}

        <Form.Item>
          <div className='flex justify-center'>
            <Button
              type='primary'
              size='large'
              disabled={
                (showJsonlSelectionSection && !selectedJsonl) ||
                !fileUuid ||
                uploading
              }
              onClick={async () => {
                console.log('🔍 Button clicked - Debug info:');
                console.log(
                  'showJsonlSelectionSection:',
                  showJsonlSelectionSection,
                );
                console.log('selectedJsonl:', selectedJsonl);
                console.log(
                  'Button disabled:',
                  showJsonlSelectionSection && !selectedJsonl,
                );
                console.log('Form values:', form.getFieldsValue());

                try {
                  // 验证基本表单字段（不包括file字段）
                  const values = await form.validateFields([
                    'datasetName',
                    'datasetDesc',
                  ]);
                  console.log('✅ Form validation passed:', values);

                  // 自定义验证多模态特有的条件
                  if (!fileUuid) {
                    message.error(t('请先上传压缩文件'));
                    return;
                  }

                  if (!selectedJsonl) {
                    message.error(t('请先选择一个JSONL文件作为展示文件'));
                    return;
                  }

                  // 直接调用 handleSubmit
                  await handleSubmit(values);
                } catch (error) {
                  console.error('❌ Form validation failed:', error);
                }
              }}>
              {fileUuid
                ? t('提交数据集')
                : showJsonlSelectionSection
                  ? t('请先上传压缩文件')
                  : t('选择文件夹')}
            </Button>
          </div>
        </Form.Item>
      </Form>
    </div>
  );
}
